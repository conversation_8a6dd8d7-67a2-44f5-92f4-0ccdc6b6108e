use crate::account::AccountManager;
use crate::config::{DataSourceType, PlaybackConfig};
use crate::matching::{MultiQueueTimeAligner, OrderBook, OrderLatencySimulator, RateLimiter};
use crate::types::{
    Bbo, CancelOrderRequest, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade,
};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};
use tracing::{debug, error, info, warn};

/// 撮合引擎
pub struct MatchingEngine {
    /// 订单簿
    orderbook: Arc<Mutex<OrderBook>>,
    /// 待处理订单
    pending_orders: HashMap<String, Order>,
    /// 账户管理器
    account_manager: Arc<Mutex<AccountManager>>,
    /// 市场数据输入
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单输入
    order_rx: mpsc::Receiver<Order>,
    /// 取消订单输入
    cancel_order_rx: mpsc::Receiver<CancelOrderRequest>,
    /// 成交输出
    trade_tx: broadcast::Sender<Trade>,
    /// 订单状态更新输出
    order_update_tx: broadcast::Sender<Order>,
    /// 市场数据转发输出（给WebSocket服务器）
    market_data_forward_tx: broadcast::Sender<MarketData>,
    /// 当前市场价格缓存
    current_prices: HashMap<String, f64>,
    /// 当前BBO数据缓存
    current_bbo: HashMap<String, Bbo>,
    /// 多队列时间对齐器
    time_aligner: MultiQueueTimeAligner,
    /// 速率限制器
    rate_limiter: RateLimiter,
    /// 订单延迟模拟器
    order_latency_simulator: OrderLatencySimulator,
    /// 当前市场数据时间戳（微秒）
    current_market_timestamp: u64,
    /// 冻结的保证金记录（订单ID -> 冻结金额）
    frozen_margins: HashMap<String, f64>,
    recv_update_id: u64,
    process_update_id: u64,
    send_update_id: u64,
}

impl MatchingEngine {
    /// 创建新的撮合引擎
    pub fn new(
        orderbook: Arc<Mutex<OrderBook>>,
        account_manager: Arc<Mutex<AccountManager>>,
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
    ) -> Self {
        // 创建一个虚拟的取消订单通道（用于向后兼容）
        let (_cancel_order_tx, cancel_order_rx) = mpsc::channel::<CancelOrderRequest>(1);

        let playback_config = PlaybackConfig::default();
        let time_aligner = MultiQueueTimeAligner::new(playback_config.time_alignment.clone());
        let rate_limiter = RateLimiter::new(playback_config.clone());
        let order_latency_simulator =
            OrderLatencySimulator::new(playback_config.order_latency.clone());

        Self {
            orderbook,
            pending_orders: HashMap::new(),
            account_manager,
            market_data_rx,
            order_rx,
            cancel_order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            current_prices: HashMap::new(),
            current_bbo: HashMap::new(),
            time_aligner,
            rate_limiter,
            order_latency_simulator,
            current_market_timestamp: 0,
            frozen_margins: HashMap::new(),
            recv_update_id: 0,
            process_update_id: 0,
            send_update_id: 0,
        }
    }

    /// 创建新的撮合引擎（带回放配置）
    pub fn new_with_playback_config(
        orderbook: Arc<Mutex<OrderBook>>,
        account_manager: Arc<Mutex<AccountManager>>,
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        cancel_order_rx: mpsc::Receiver<CancelOrderRequest>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
        playback_config: PlaybackConfig,
    ) -> Self {
        let time_aligner = MultiQueueTimeAligner::new(playback_config.time_alignment.clone());
        let rate_limiter = RateLimiter::new(playback_config.clone());
        let order_latency_simulator =
            OrderLatencySimulator::new(playback_config.order_latency.clone());

        Self {
            orderbook,
            pending_orders: HashMap::new(),
            account_manager,
            market_data_rx,
            order_rx,
            cancel_order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            current_prices: HashMap::new(),
            current_bbo: HashMap::new(),
            time_aligner,
            rate_limiter,
            order_latency_simulator,
            current_market_timestamp: 0,
            frozen_margins: HashMap::new(),
            recv_update_id: 0,
            process_update_id: 0,
            send_update_id: 0,
        }
    }

    /// 启动撮合引擎
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting matching engine");

        // 检查订单通道是否已关闭
        let mut order_channel_closed = false;
        let mut cancel_order_channel_closed = false;

        loop {
            tokio::select! {
                // 处理市场数据
                market_data = self.market_data_rx.recv() => {
                    match market_data {
                        Ok(data) => {
                            if let Err(e) = self.process_market_data(data).await {
                                error!("Failed to process market data: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            warn!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }

                // 处理新订单（如果通道还开着）
                order = self.order_rx.recv(), if !order_channel_closed => {
                    match order {
                        Some(order) => {
                            if let Err(e) = self.process_order(order).await {
                                // TODO: 这里需要优化，不要直接返回错误，而是应该记录错误，然后继续处理订单
                                debug!("Failed to process order: {}", e);
                            }
                        }
                        None => {
                            info!("Order channel closed");
                            order_channel_closed = true;
                            // 不退出循环，继续处理市场数据
                        }
                    }
                }

                // 处理取消订单请求（如果通道还开着）
                cancel_request = self.cancel_order_rx.recv(), if !cancel_order_channel_closed => {
                    match cancel_request {
                        Some(request) => {
                            if let Err(e) = self.process_cancel_order_request(request).await {
                                error!("Failed to process cancel order request: {}", e);
                            }
                        }
                        None => {
                            info!("Cancel order channel closed");
                            cancel_order_channel_closed = true;
                            // 不退出循环，继续处理市场数据
                        }
                    }
                }
            }
        }

        // 在停止前处理缓冲区中剩余的数据
        info!("Processing remaining buffered data before stopping");
        self.process_buffered_data().await?;

        info!("Matching engine stopped");
        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        debug!("Received market data in matching engine: {:?}", market_data);

        // 将市场数据添加到对应的队列
        match market_data {
            MarketData::OrderBook(ref orderbook) => {
                if orderbook.update_id != self.recv_update_id + 1 {
                    panic!(
                        "Orderbook update_id mismatch: expected {}, got {}",
                        self.recv_update_id + 1,
                        orderbook.update_id
                    );
                }
                self.recv_update_id = orderbook.update_id;
            }
            _ => {}
        }
        self.time_aligner.add_data(market_data)?;

        debug!(
            "Added data to time aligner, total size: {}",
            self.time_aligner.total_size()
        );

        // 检查是否应该处理队列中的数据
        self.check_and_process_queues().await?;

        Ok(())
    }

    /// 直接添加数据到队列而不处理（用于测试）
    #[cfg(test)]
    pub async fn add_to_buffer_only(&mut self, market_data: MarketData) -> Result<()> {
        self.time_aligner.add_data(market_data)?;
        Ok(())
    }

    /// 检查并处理队列中的数据
    /// 使用智能的多队列时间对齐策略
    async fn check_and_process_queues(&mut self) -> Result<()> {
        if !self.time_aligner.has_data() {
            return Ok(());
        }

        // 检查是否应该处理数据
        if self.time_aligner.should_process_data() {
            // 批量处理数据，每次处理一定数量以保持时间顺序
            let batch_size = self.rate_limiter.get_batch_size() as usize;
            let batch_data = self.time_aligner.get_batch_data(batch_size);

            for data in batch_data {
                match data {
                    MarketData::OrderBook(ref orderbook) => {
                        if orderbook.update_id != self.process_update_id + 1 {
                            panic!(
                                "Orderbook update_id mismatch: expected {}, got {}",
                                self.process_update_id + 1,
                                orderbook.update_id
                            );
                        }
                        self.process_update_id = orderbook.update_id;
                    }
                    _ => {}
                }
                self.process_single_market_data_internal(data).await?;
            }
        }

        Ok(())
    }

    /// 获取时间对齐器的总数据量（用于测试）
    #[cfg(test)]
    pub fn get_time_aligner_size(&self) -> usize {
        self.time_aligner.total_size()
    }

    /// 获取时间对齐器的总数据量（公开方法）
    pub fn get_buffer_size(&self) -> usize {
        self.time_aligner.total_size()
    }

    /// 检查缓冲区是否为空
    pub fn is_buffer_empty(&self) -> bool {
        !self.time_aligner.has_data()
    }

    /// 更新回放配置
    pub fn update_playback_config(&mut self, config: PlaybackConfig) {
        info!(
            "Updating playback config: rate_per_second={}, enabled={}, batch_size={}",
            config.rate_per_second, config.enabled, config.batch_size
        );

        // 更新时间对齐器配置
        self.time_aligner
            .update_config(config.time_alignment.clone());

        // 更新速率限制器配置
        self.rate_limiter.update_config(config.clone());

        // 更新订单延迟模拟器配置
        self.order_latency_simulator
            .update_config(config.order_latency.clone());

        info!(
            "Order latency simulation: enabled={}, latency_micros={}, random_latency={}",
            config.order_latency.enabled,
            config.order_latency.latency_micros,
            config.order_latency.random_latency
        );
    }

    /// 获取当前回放配置
    pub fn get_playback_config(&self) -> &PlaybackConfig {
        self.rate_limiter.get_config()
    }

    /// 获取订单延迟模拟器统计信息
    pub fn get_order_latency_stats(&self) -> crate::matching::OrderLatencyStats {
        self.order_latency_simulator.get_stats()
    }

    /// 获取当前市场时间戳
    pub fn get_current_market_timestamp(&self) -> u64 {
        self.current_market_timestamp
    }

    /// 清空延迟订单队列
    pub fn clear_delayed_orders(&mut self) {
        self.order_latency_simulator.clear();
    }

    pub async fn process_buffered_data(&mut self) -> Result<()> {
        while let Some(data) = self.time_aligner.get_next_data() {
            self.process_single_market_data_internal(data).await?;
        }
        Ok(())
    }

    /// 从市场数据更新BBO缓存
    async fn update_bbo_cache_from_market_data(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::Bbo(bbo) => {
                debug!(
                    "Updating BBO cache from BBO data: bid={}, ask={}",
                    bbo.bid_price, bbo.ask_price
                );
                self.current_bbo.insert("BTCUSDT".to_string(), bbo.clone());
            }
            MarketData::BookTicker(bookticker) => {
                debug!(
                    "Updating BBO cache from BookTicker data: update_id={}",
                    bookticker.update_id
                );
                let bbo = bookticker.to_bbo();
                self.current_bbo.insert("BTCUSDT".to_string(), bbo);
            }
            _ => {
                // 其他类型的市场数据不更新BBO缓存
            }
        }
    }

    /// 处理单个市场数据项
    async fn process_single_market_data_internal(&mut self, market_data: MarketData) -> Result<()> {
        self.rate_limiter.apply_rate_control().await?;

        if market_data.data_source_type() == DataSourceType::BinanceTardis {
            self.current_market_timestamp = market_data.timestamp_for_sorting();
            self.update_bbo_cache_from_market_data(&market_data).await;
            self.process_delayed_orders().await?;
            self.update_account_prices(&market_data).await;

            debug!("✅ Processing BinanceTardis data for matching");
            match &market_data {
                MarketData::OrderBook(snapshot) => {
                    debug!("Processing orderbook snapshot");
                    // 重建订单簿
                    self.orderbook.lock().await.rebuild_from_snapshot(
                        &snapshot.bids,
                        &snapshot.asks,
                        snapshot.timestamp,
                        snapshot.update_id,
                    );
                    // 尝试撮合待处理订单
                    self.match_pending_orders().await?;
                }
                MarketData::Bbo(bbo) => {
                    debug!("Processing BBO update with timestamp: {:?}", bbo.timestamp);
                    debug!(
                        "📊 Processing BBO for matching: bid={}, ask={}",
                        bbo.bid_price, bbo.ask_price
                    );
                    // 使用BBO进行撮合（BBO缓存已在前面更新）
                    self.match_with_bbo(bbo).await?;
                }
                MarketData::Trade(trade) => {
                    debug!("Processing external trade: {}", trade.id);
                    // 外部交易可能影响订单簿状态
                    // 这里可以添加相应的处理逻辑
                }
                MarketData::BookTicker(bookticker) => {
                    debug!(
                        "Processing BookTicker data: update_id={}",
                        bookticker.update_id
                    );
                    // 将BookTicker转换为BBO进行撮合（BBO缓存已在前面更新）
                    let bbo = bookticker.to_bbo();
                    self.match_with_bbo(&bbo).await?;
                }

                MarketData::TradeData(trade_data) => {
                    debug!(
                        "Processing TradeData: id={} at timestamp={}",
                        trade_data.id, trade_data.timestamp
                    );
                    // 交易数据通常用于验证撮合结果，这里可以添加相关逻辑
                }
            }
        }

        // 处理完撮合后，将市场数据转发给WebSocket服务器
        debug!(
            "🚀 Matching engine forwarding market data to WebSocket: {:?}",
            market_data
        );

        // 克隆市场数据用于记录
        let market_data_for_recording = market_data.clone();

        match &market_data {
            MarketData::OrderBook(ref orderbook) => {
                if orderbook.update_id != self.send_update_id + 1 {
                    panic!(
                        "Orderbook update_id mismatch: expected {}, got {}",
                        self.send_update_id + 1,
                        orderbook.update_id
                    );
                }
                self.send_update_id = orderbook.update_id;
            }
            _ => {}
        }
        match self.market_data_forward_tx.send(market_data) {
            Ok(receiver_count) => {
                debug!(
                    "✅ Market data forwarded to {} WebSocket receivers",
                    receiver_count
                );
            }
            Err(e) => {
                info!("❌ Failed to forward market data to WebSocket: {}", e);
                // 这里不返回错误，因为WebSocket转发失败不应该影响撮合引擎的正常运行
            }
        }

        // 记录处理了一个数据项
        self.rate_limiter.record_processed();

        // 记录市场数据到回测记录器（在流控之后）
        self.record_market_data_after_flow_control(&market_data_for_recording)
            .await;

        Ok(())
    }

    /// 处理新订单
    async fn process_order(&mut self, mut order: Order) -> Result<()> {
        debug!(
            "🚀 PROCESSING NEW ORDER: {} {} {:.4} @ {:?} (type: {:?}, status: {:?})",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price,
            order.order_type,
            order.status
        );

        // 更新订单时间戳为当前市场数据时间戳
        if self.current_market_timestamp > 0 {
            order.timestamp = DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                .unwrap_or_else(|| Utc::now());
            debug!(
                "Updated order {} timestamp to market time: {}",
                order.id, order.timestamp
            );
        }

        // 记录初始订单到回测记录器（使用市场数据时间戳）
        self.record_order(&order).await;

        // 在处理订单前先检查资金和冻结保证金
        if let Err(e) = self.validate_and_freeze_order_margin(&order).await {
            // TODO: 这里需要优化，不要直接返回错误，而是应该记录错误，然后继续处理订单
            debug!("Order {} rejected: {}", order.id, e);
            // 发送订单拒绝更新
            let mut rejected_order = order.clone();
            rejected_order.status = OrderStatus::Rejected;
            self.send_order_update(rejected_order).await?;
            return Err(e);
        }

        // 检查是否启用延迟模拟
        if self.order_latency_simulator.is_enabled() {
            // 将订单添加到延迟队列
            let order_id = order.id.clone(); // 保存order_id用于日志
            match self
                .order_latency_simulator
                .add_order(order, self.current_market_timestamp)
            {
                Ok(()) => {
                    debug!("Order {} added to latency queue", order_id);
                }
                Err(e) => {
                    warn!(
                        "Failed to add order to latency simulator: {}, processing immediately",
                        e
                    );
                    // 如果添加失败，立即处理订单
                    // 注意：这里order已经被移动了，我们需要重新创建或者改变逻辑
                    // 为了简化，我们记录错误但不立即处理
                    error!(
                        "Order {} could not be processed due to latency simulator error",
                        order_id
                    );
                }
            }
        } else {
            // 如果未启用延迟模拟，立即处理订单
            self.process_order_immediately(&mut order).await?;
        }

        Ok(())
    }

    /// 验证订单并冻结保证金
    async fn validate_and_freeze_order_margin(&mut self, order: &Order) -> Result<()> {
        let mut account_manager = self.account_manager.lock().await;

        // 计算订单所需的保证金
        let required_margin = self.calculate_order_margin(order, &account_manager).await?;

        // 检查可用余额是否足够
        let available_balance = account_manager.get_balance("USDT");
        if available_balance < required_margin {
            return Err(BacktestError::Account(format!(
                "Insufficient margin: required {}, available {}",
                required_margin, available_balance
            )));
        }

        // 冻结保证金
        if let Err(e) = account_manager.freeze_margin("USDT", required_margin) {
            return Err(BacktestError::Account(format!(
                "Failed to freeze margin: {}",
                e
            )));
        }

        // 记录订单的冻结保证金（用于后续释放）
        self.frozen_margins
            .insert(order.id.clone(), required_margin);

        debug!(
            "Order {} margin frozen: {} USDT (available balance: {} -> {})",
            order.id,
            required_margin,
            available_balance,
            available_balance - required_margin
        );

        Ok(())
    }

    /// 计算订单所需的保证金
    async fn calculate_order_margin(
        &self,
        order: &Order,
        account_manager: &AccountManager,
    ) -> Result<f64> {
        // 获取订单价格
        let order_price = match order.order_type {
            OrderType::Market => {
                // 市价单使用当前市场价格估算
                Price::new(
                    self.get_current_market_price(&order.symbol)
                        .ok_or_else(|| {
                            BacktestError::Matching(
                                "No market price available for market order".to_string(),
                            )
                        })?,
                )
            }
            _ => order.price.ok_or_else(|| {
                BacktestError::Matching("Limit order must have price".to_string())
            })?,
        };

        // 计算名义价值
        let notional_value = order_price.value() * order.quantity;

        // 获取最大杠杆
        let max_leverage = account_manager.get_max_leverage();

        // 计算所需保证金
        let required_margin = notional_value / max_leverage;

        // 添加手续费预留
        let fee_rate = match order.order_type {
            OrderType::Market => account_manager.get_taker_fee_rate(),
            _ => account_manager.get_maker_fee_rate(), // 限价单假设为maker
        };
        let estimated_fee = notional_value * fee_rate;

        Ok(required_margin + estimated_fee)
    }

    /// 释放订单的冻结保证金
    async fn release_order_margin(&mut self, order_id: &str) -> Result<()> {
        if let Some(frozen_amount) = self.frozen_margins.remove(order_id) {
            let mut account_manager = self.account_manager.lock().await;
            if let Err(e) = account_manager.unfreeze_margin("USDT", frozen_amount) {
                error!("Failed to release margin for order {}: {}", order_id, e);
                // 重新插入记录，避免丢失
                self.frozen_margins
                    .insert(order_id.to_string(), frozen_amount);
                return Err(BacktestError::Account(format!(
                    "Failed to release margin: {}",
                    e
                )));
            }

            debug!("Order {} margin released: {} USDT", order_id, frozen_amount);
        }
        Ok(())
    }

    /// 立即处理订单（不经过延迟模拟）
    async fn process_order_immediately(&mut self, order: &mut Order) -> Result<()> {
        match order.order_type {
            OrderType::Market => {
                // 市价单立即撮合
                self.match_market_order(order).await?;
            }
            OrderType::Limit => {
                // 限价单先尝试撮合，未成交部分加入订单簿
                self.match_limit_order(order).await?;
            }
            OrderType::LimitIOC => {
                // IOC限价单立即撮合，不能成交的部分立即取消
                self.match_ioc_order(order).await?;
            }
            OrderType::LimitGTX => {
                // GTX限价单只能作为maker成交，如果会成为taker则取消
                self.match_gtx_order(order).await?;
            }
        }

        Ok(())
    }

    /// 获取当前BBO数据
    fn get_current_bbo(&self, symbol: &str) -> Option<&Bbo> {
        let result = self.current_bbo.get(symbol);
        debug!(
            "🔍 Getting BBO for symbol {}: {:?}",
            symbol,
            result.is_some()
        );
        if let Some(bbo) = result {
            debug!("📊 BBO data: bid={}, ask={}", bbo.bid_price, bbo.ask_price);
        }
        result
    }

    /// 撮合IOC订单
    /// IOC订单立即撮合，不能成交的部分立即取消
    async fn match_ioc_order(&mut self, order: &mut Order) -> Result<()> {
        debug!(
            "Processing IOC order: {} {:?} {} @ {:?}",
            order.id, order.side, order.quantity, order.price
        );

        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("IOC order must have price".to_string()))?;

        // 检查是否能立即成交
        let can_execute = match order.side {
            OrderSide::Buy => {
                // 买单：检查是否有卖单价格 <= 买单价格
                if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                    current_bbo.ask_price.value() <= order_price.value()
                } else {
                    // 没有BBO数据，检查订单簿
                    if let Some(best_ask) = self.orderbook.lock().await.best_ask() {
                        best_ask.value() <= order_price.value()
                    } else {
                        // 没有订单簿数据，使用当前市场价格作为ask价格进行判断
                        if let Some(current_price) = self.get_current_market_price(&order.symbol) {
                            let estimated_ask = current_price * 1.001; // ask价格稍微高一点
                            debug!("💰 Using market price {} for IOC buy order, estimated ask: {}, order price: {}", current_price, estimated_ask, order_price.value());
                            estimated_ask <= order_price.value()
                        } else {
                            false
                        }
                    }
                }
            }
            OrderSide::Sell => {
                // 卖单：检查是否有买单价格 >= 卖单价格
                if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                    current_bbo.bid_price.value() >= order_price.value()
                } else {
                    // 没有BBO数据，检查订单簿
                    if let Some(best_bid) = self.orderbook.lock().await.best_bid() {
                        best_bid.value() >= order_price.value()
                    } else {
                        // 没有订单簿数据，使用当前市场价格作为bid价格进行判断
                        if let Some(current_price) = self.get_current_market_price(&order.symbol) {
                            let estimated_bid = current_price * 0.999; // bid价格稍微低一点
                            debug!("💰 Using market price {} for IOC sell order, estimated bid: {}, order price: {}", current_price, estimated_bid, order_price.value());
                            estimated_bid >= order_price.value()
                        } else {
                            // 没有任何价格数据，使用默认价格进行判断
                            debug!("⚠️ No market price data available, using default price for IOC order judgment");
                            let default_price = 70000.0; // 默认BTC价格
                            let estimated_bid = default_price * 0.999;
                            debug!("💰 Using default price {} for IOC sell order, estimated bid: {}, order price: {}", default_price, estimated_bid, order_price.value());
                            estimated_bid >= order_price.value()
                        }
                    }
                }
            }
        };

        if can_execute {
            // 能立即成交，按最优价格成交
            let trade_price = match order.side {
                OrderSide::Buy => {
                    if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                        current_bbo.ask_price
                    } else if let Some(best_ask) = self.orderbook.lock().await.best_ask() {
                        best_ask
                    } else if let Some(current_price) = self.get_current_market_price(&order.symbol)
                    {
                        Price::new(current_price * 1.001) // ask价格稍微高一点
                    } else {
                        order_price // 使用订单价格作为最后fallback
                    }
                }
                OrderSide::Sell => {
                    if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                        current_bbo.bid_price
                    } else if let Some(best_bid) = self.orderbook.lock().await.best_bid() {
                        best_bid
                    } else if let Some(current_price) = self.get_current_market_price(&order.symbol)
                    {
                        Price::new(current_price * 0.999) // bid价格稍微低一点
                    } else {
                        order_price // 使用订单价格作为最后fallback
                    }
                }
            };

            let quantity = order.quantity;
            // IOC订单能立即成交说明是taker
            self.execute_trade(order, trade_price, quantity, false)
                .await?;
            order.status = OrderStatus::Filled;

            // 释放订单的冻结保证金
            self.release_order_margin(&order.id).await?;

            debug!("IOC order {} filled at price {}", order.id, trade_price);
        } else {
            // 不能立即成交，订单过期
            order.status = OrderStatus::Expired;

            // 释放订单的冻结保证金
            self.release_order_margin(&order.id).await?;

            debug!(
                "IOC order {} expired (cannot execute immediately)",
                order.id
            );
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 处理延迟订单队列中可以执行的订单
    async fn process_delayed_orders(&mut self) -> Result<()> {
        let ready_orders = self
            .order_latency_simulator
            .get_ready_orders(self.current_market_timestamp);

        if !ready_orders.is_empty() {
            debug!("Processing {} delayed orders", ready_orders.len());
        }

        for mut order in ready_orders {
            debug!(
                "Processing delayed order: {} (delayed execution at timestamp: {})",
                order.id, self.current_market_timestamp
            );

            // 立即处理延迟订单
            if let Err(e) = self.process_order_immediately(&mut order).await {
                error!("Failed to process delayed order {}: {}", order.id, e);
            }
        }

        Ok(())
    }

    /// 撮合市价单
    /// 按设计文档要求：Buy单以ask1成交，Sell单以bid1成交
    async fn match_market_order(&mut self, order: &mut Order) -> Result<()> {
        debug!(
            "Processing market order: {} {:?} {}",
            order.id, order.side, order.quantity
        );

        // 获取当前BBO价格，按设计文档要求成交
        let trade_price = match order.side {
            OrderSide::Buy => {
                // 买入市价单以ask1（卖一价）成交
                if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                    current_bbo.ask_price
                } else {
                    // 如果没有BBO数据，使用当前市场价格
                    let current_price = self
                        .get_current_market_price(&order.symbol)
                        .unwrap_or(70000.0);
                    Price::new(current_price * 1.001) // 稍微高一点作为ask价格
                }
            }
            OrderSide::Sell => {
                // 卖出市价单以bid1（买一价）成交
                if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
                    current_bbo.bid_price
                } else {
                    // 如果没有BBO数据，使用当前市场价格
                    let current_price = self
                        .get_current_market_price(&order.symbol)
                        .unwrap_or(70000.0);
                    Price::new(current_price * 0.999) // 稍微低一点作为bid价格
                }
            }
        };

        // 直接以BBO价格成交
        let quantity = order.quantity;
        // 市价单始终是taker
        self.execute_trade(order, trade_price, quantity, false)
            .await?;
        order.status = OrderStatus::Filled;

        // 释放订单的冻结保证金
        self.release_order_margin(&order.id).await?;

        debug!(
            "Market order {} filled at BBO price {} (side: {:?})",
            order.id, trade_price, order.side
        );

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 撮合GTX限价单
    /// GTX (Good Till Crossing) 订单只能作为maker成交
    /// 如果订单会立即成交（成为taker），则取消订单
    async fn match_gtx_order(&mut self, order: &mut Order) -> Result<()> {
        debug!(
            "Processing GTX order: {} {:?} {} @ {:?}",
            order.id, order.side, order.quantity, order.price
        );

        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("GTX order must have price".to_string()))?;

        // 检查GTX订单是否会立即成交（成为taker）
        let would_be_taker = self.is_limit_order_taker(order, order_price)?;

        if would_be_taker {
            // 如果会成为taker，取消订单
            order.status = OrderStatus::Cancelled;
            // 更新订单时间戳为当前市场数据时间戳
            if self.current_market_timestamp > 0 {
                order.timestamp =
                    DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                        .unwrap_or_else(|| Utc::now());
                debug!(
                    "Updated GTX cancelled order {} timestamp to market time: {}",
                    order.id, order.timestamp
                );
            }

            // 释放订单的冻结保证金
            self.release_order_margin(&order.id).await?;

            debug!(
                "GTX order {} cancelled (would cross and become taker)",
                order.id
            );
        } else {
            // 如果不会立即成交，作为maker挂在订单簿中
            order.status = OrderStatus::Pending;
            // 更新订单时间戳为当前市场数据时间戳
            if self.current_market_timestamp > 0 {
                order.timestamp =
                    DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                        .unwrap_or_else(|| Utc::now());
                debug!(
                    "Updated GTX pending order {} timestamp to market time: {}",
                    order.id, order.timestamp
                );
            }
            self.orderbook.lock().await.add_order(order.clone());
            self.pending_orders.insert(order.id.clone(), order.clone());
            debug!(
                "GTX order {} added to orderbook as maker (price: {})",
                order.id, order_price
            );
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 撮合限价单
    async fn match_limit_order(&mut self, order: &mut Order) -> Result<()> {
        // 使用订单簿的FIFO撮合功能
        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("Limit order must have price".to_string()))?;

        // 判断限价单是否能立即成交（是否为taker）
        let is_taker = self.is_limit_order_taker(order, order_price)?;

        // 使用订单簿的FIFO撮合功能
        let mut orderbook = self.orderbook.lock().await.clone();
        match orderbook.match_order(order) {
            Ok((matches, remaining_quantity)) => {
                debug!("Matched limit orders: {:?}", matches);
                // 处理所有成交
                for (matched_order, trade_price, trade_quantity) in matches {
                    // 限价单能立即成交说明是taker，被撮合的订单是maker
                    self.execute_trade(order, trade_price, trade_quantity, is_taker)
                        .await?;

                    // 更新被撮合订单的状态
                    let mut updated_order = matched_order;
                    if updated_order.quantity <= 0.0 {
                        updated_order.status = OrderStatus::Filled;
                        // 从pending_orders中移除已完成的订单
                        self.pending_orders.remove(&updated_order.id);
                        // 释放被撮合订单的冻结保证金
                        self.release_order_margin(&updated_order.id).await?;
                    } else {
                        updated_order.status = OrderStatus::PartiallyFilled;
                        // 更新pending_orders中的订单
                        self.pending_orders
                            .insert(updated_order.id.clone(), updated_order.clone());
                    }
                    self.send_order_update(updated_order).await?;
                }

                // 更新原订单状态
                if remaining_quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    // 释放原订单的冻结保证金
                    self.release_order_margin(&order.id).await?;
                } else if remaining_quantity < order.quantity {
                    order.status = OrderStatus::PartiallyFilled;
                    order.quantity = remaining_quantity;
                    // 未成交部分加入订单簿和待处理订单
                    self.orderbook.lock().await.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                } else {
                    // 完全未成交，直接加入订单簿和待处理订单
                    order.status = OrderStatus::Pending;
                    self.orderbook.lock().await.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                }
            }
            Err(e) => {
                warn!("Failed to match limit order {}: {}", order.id, e);
                // 撮合失败，将订单加入订单簿
                order.status = OrderStatus::Pending;
                self.orderbook.lock().await.add_order(order.clone());
                self.pending_orders.insert(order.id.clone(), order.clone());
            }
        }

        debug!("Sending order update: {:?}", order);
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 使用BBO进行撮合
    async fn match_with_bbo(&mut self, bbo: &Bbo) -> Result<()> {
        // 收集需要处理的订单信息
        let mut orders_to_process = Vec::new();

        for (order_id, order) in &self.pending_orders {
            let order_price = order.price.unwrap_or(Price::new(0.0));
            let can_match = match order.side {
                OrderSide::Buy => order_price >= bbo.ask_price,
                OrderSide::Sell => order_price <= bbo.bid_price,
            };
            debug!("order price: {}, bbo ask price: {}, bbo bid price: {}, order side: {:?}, can_match: {}", order_price, bbo.ask_price, bbo.bid_price, order.side, can_match);
            if can_match {
                orders_to_process.push((order_id.clone(), order.clone()));
            }
        }

        // 处理匹配的订单
        let mut orders_to_remove = Vec::new();

        for (order_id, mut order) in orders_to_process {
            let trade_price = match order.side {
                OrderSide::Buy => bbo.ask_price,
                OrderSide::Sell => bbo.bid_price,
            };

            // 修复：使用订单的实际数量，而不是BBO数据中的数量
            let trade_quantity = order.quantity;
            if trade_quantity > 0.0 {
                // 执行交易 - 挂在订单簿中的订单被撮合，是maker
                self.execute_trade(&mut order, trade_price, trade_quantity, true)
                    .await?;

                // 更新订单状态
                order.quantity -= trade_quantity;
                if order.quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    orders_to_remove.push(order_id.clone());
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                }

                // 更新订单到pending_orders
                if let Some(pending_order) = self.pending_orders.get_mut(&order_id) {
                    *pending_order = order.clone();
                }

                // 发送订单更新
                debug!("Sending order update: {:?}", order);
                self.send_order_update(order).await?;
            }
        }

        // 移除已完成的订单并释放保证金
        for order_id in orders_to_remove {
            self.pending_orders.remove(&order_id);
            self.orderbook.lock().await.remove_order(&order_id);
            // 释放已完成订单的冻结保证金
            self.release_order_margin(&order_id).await?;
        }

        Ok(())
    }

    /// 撮合待处理订单
    async fn match_pending_orders(&mut self) -> Result<()> {
        // 收集所有待处理订单的ID，避免在迭代时修改HashMap
        let pending_order_ids: Vec<String> = self.pending_orders.keys().cloned().collect();

        for order_id in pending_order_ids {
            if let Some(mut order) = self.pending_orders.get(&order_id).cloned() {
                // 尝试重新撮合每个待处理订单
                let match_result = {
                    let mut orderbook = self.orderbook.lock().await;
                    orderbook.match_order(&order)
                };

                match match_result {
                    Ok((matches, remaining_quantity)) => {
                        // 处理所有新的成交
                        for (matched_order, trade_price, trade_quantity) in matches {
                            // 挂在订单簿中的订单被撮合，是maker
                            self.execute_trade(&mut order, trade_price, trade_quantity, true)
                                .await?;

                            // 更新被撮合订单的状态
                            let mut updated_order = matched_order;
                            if updated_order.quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&updated_order.id);
                            } else {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                self.pending_orders
                                    .insert(updated_order.id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }

                        // 更新原订单状态
                        if let Some(pending_order) = self.pending_orders.get(&order_id).cloned() {
                            let mut updated_order = pending_order;
                            if remaining_quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&order_id);
                            } else if remaining_quantity < updated_order.quantity {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                updated_order.quantity = remaining_quantity;
                                self.pending_orders
                                    .insert(order_id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }
                    }
                    Err(_) => {
                        // 撮合失败，保持订单在待处理状态
                        continue;
                    }
                }
            }
        }

        Ok(())
    }

    /// 判断限价单是否为taker（能立即成交）
    fn is_limit_order_taker(&self, order: &Order, order_price: Price) -> Result<bool> {
        // 获取当前最优买卖价
        if let Some(current_bbo) = self.get_current_bbo(&order.symbol) {
            match order.side {
                OrderSide::Buy => {
                    // 买单价格 >= 最优卖价（ask1）时为taker
                    Ok(order_price.value() >= current_bbo.ask_price.value())
                }
                OrderSide::Sell => {
                    // 卖单价格 <= 最优买价（bid1）时为taker
                    Ok(order_price.value() <= current_bbo.bid_price.value())
                }
            }
        } else {
            // 没有BBO数据时，假设为taker（保守处理）
            Ok(true)
        }
    }

    /// 执行交易
    async fn execute_trade(
        &mut self,
        order: &mut Order,
        price: Price,
        quantity: f64,
        is_maker: bool,
    ) -> Result<()> {
        debug!(
            "💰 EXECUTING TRADE: Order {} {} {:.4} @ {:.4} ({}) - Original order qty: {:.4}",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            quantity,
            price.value(),
            if is_maker { "MAKER" } else { "TAKER" },
            order.quantity
        );

        // 使用当前市场数据时间戳，确保与订单时间戳一致
        let market_time = if self.current_market_timestamp > 0 {
            DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                .unwrap_or_else(|| Utc::now())
        } else {
            // 如果没有市场数据时间戳，使用订单时间戳
            order.timestamp
        };

        let trade = Trade {
            id: format!("{}_{}", order.id, self.current_market_timestamp),
            symbol: order.symbol.clone(),
            price,
            quantity,
            side: order.side.clone(),
            timestamp: Some(market_time),
            data_source_type: DataSourceType::BinanceTardis,
        };

        debug!(
            "📊 Trade created: {} {} {} @ {} (quantity: {:.4}, {}) at market time: {}",
            trade.id,
            trade.symbol,
            match trade.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            trade.price,
            trade.quantity,
            if is_maker { "MAKER" } else { "TAKER" },
            market_time
        );

        // 从账户管理器获取正确的手续费率
        let fee_rate = {
            let account_manager = self.account_manager.lock().await;
            if is_maker {
                account_manager.get_maker_fee_rate()
            } else {
                account_manager.get_taker_fee_rate()
            }
        };
        let commission = quantity * price.value() * fee_rate;

        // 更新订单的执行信息
        if let Some(ref mut exec_info) = order.execution_info {
            debug!(
                "📈 Updating existing execution info for order {}: prev_filled: {:.4}, adding: {:.4}",
                order.id, exec_info.filled_quantity, quantity
            );

            exec_info.last_filled_price = Some(price);
            exec_info.last_filled_quantity = quantity;
            exec_info.filled_quantity += quantity;
            exec_info.commission += commission;
            exec_info.trade_id = Some(trade.id.clone());

            // 计算平均价格
            if exec_info.filled_quantity > 0.0 {
                let total_value = exec_info
                    .average_price
                    .map_or(0.0, |p| p.value() * (exec_info.filled_quantity - quantity))
                    + price.value() * quantity;
                exec_info.average_price = Some(Price::new(total_value / exec_info.filled_quantity));
            }

            debug!(
                "✅ Updated execution info: total_filled: {:.4}, avg_price: {:?}, commission: {:.4}",
                exec_info.filled_quantity, exec_info.average_price, exec_info.commission
            );
        } else {
            debug!(
                "📝 Creating new execution info for order {}: qty: {:.4}, price: {:.4}",
                order.id,
                quantity,
                price.value()
            );

            order.execution_info = Some(crate::types::OrderExecutionInfo {
                last_filled_price: Some(price),
                last_filled_quantity: quantity,
                filled_quantity: quantity,
                average_price: Some(price),
                commission,
                commission_asset: "USDT".to_string(),
                trade_id: Some(trade.id.clone()),
            });

            debug!(
                "✅ Created execution info: filled: {:.4}, price: {:?}, commission: {:.4}",
                quantity, price, commission
            );
        }

        // 更新账户状态
        {
            let mut account_manager = self.account_manager.lock().await;
            if let Err(e) = account_manager.process_trade(trade.clone(), commission, is_maker) {
                // TODO: 这里需要优化，不要直接返回错误，而是应该记录错误，然后继续处理订单
                debug!("Failed to process trade in account manager: {}", e);
                // 这里不返回错误，因为账户处理失败不应该影响交易的发送
            }
        }

        if let Err(e) = self.trade_tx.send(trade) {
            error!("Failed to send trade: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send trade: {}",
                e
            )));
        }

        Ok(())
    }

    /// 发送订单更新
    async fn send_order_update(&self, mut order: Order) -> Result<()> {
        debug!(
            "📤 SENDING ORDER UPDATE: {} {} {:.4} @ {:?} (status: {:?}, filled: {:.4})",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price,
            order.status,
            order
                .execution_info
                .as_ref()
                .map(|ei| ei.filled_quantity)
                .unwrap_or(0.0)
        );

        // 确保订单时间戳使用市场数据时间戳
        if self.current_market_timestamp > 0 {
            order.timestamp = DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                .unwrap_or_else(|| Utc::now());
            debug!(
                "Updated order update timestamp to market time: {} for order {}",
                order.timestamp, order.id
            );
        }

        // 记录订单状态更新到回测记录器
        self.record_order(&order).await;

        if let Err(e) = self.order_update_tx.send(order) {
            error!("Failed to send order update: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send order update: {}",
                e
            )));
        }

        Ok(())
    }

    /// 处理取消订单请求
    async fn process_cancel_order_request(&mut self, request: CancelOrderRequest) -> Result<()> {
        debug!(
            "Processing cancel order request: order_id={:?}, client_order_id={:?}, symbol={}",
            request.order_id, request.client_order_id, request.symbol
        );

        // 根据order_id或client_order_id查找订单
        let order_id_to_cancel = if let Some(order_id) = &request.order_id {
            // 首先尝试直接使用提供的order_id
            if self.pending_orders.contains_key(order_id) {
                Some(order_id.clone())
            } else {
                // 如果直接查找失败，尝试作为client_order_id查找
                // 这处理了客户端错误地将client_order_id作为order_id发送的情况
                debug!("Order ID {} not found, trying as client order ID", order_id);
                self.pending_orders
                    .iter()
                    .find(|(_, order)| {
                        order.client_order_id == *order_id && order.symbol == request.symbol
                    })
                    .map(|(internal_order_id, _)| {
                        debug!(
                            "Found order by client_order_id: {} -> {}",
                            order_id, internal_order_id
                        );
                        internal_order_id.clone()
                    })
            }
        } else if let Some(client_order_id) = &request.client_order_id {
            // 根据client_order_id查找order_id
            self.pending_orders
                .iter()
                .find(|(_, order)| {
                    order.client_order_id == *client_order_id && order.symbol == request.symbol
                })
                .map(|(order_id, _)| order_id.clone())
        } else {
            None
        };

        match order_id_to_cancel {
            Some(order_id) => {
                tracing::debug!("✅ Found order to cancel: {}", order_id);
                // 取消订单
                self.cancel_order(&order_id).await?;
                debug!("Order cancelled successfully: {}", order_id);
            }
            None => {
                warn!(
                    "Order not found for cancellation: order_id={:?}, client_order_id={:?}, symbol={}",
                    request.order_id, request.client_order_id, request.symbol
                );
                // 这里可以选择发送一个错误的订单更新，但为了简化，我们只记录警告
            }
        }

        Ok(())
    }

    /// 取消订单
    pub async fn cancel_order(&mut self, order_id: &str) -> Result<()> {
        debug!("🔍 Attempting to cancel order: {}", order_id);
        debug!(
            "🔍 Current pending orders: {:?}",
            self.pending_orders.keys().collect::<Vec<_>>()
        );

        if let Some(mut order) = self.pending_orders.remove(order_id) {
            debug!(
                "✅ Found and removed order from pending_orders: {}",
                order_id
            );
            order.status = OrderStatus::Cancelled;
            // 更新订单时间戳为当前市场数据时间戳
            if self.current_market_timestamp > 0 {
                order.timestamp =
                    DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                        .unwrap_or_else(|| Utc::now());
                debug!(
                    "Updated cancelled order {} timestamp to market time: {}",
                    order.id, order.timestamp
                );
            }
            self.orderbook.lock().await.remove_order(order_id);

            // 释放订单的冻结保证金
            self.release_order_margin(order_id).await?;

            self.send_order_update(order).await?;
            debug!("Order cancelled: {}", order_id);
        } else {
            info!("❌ Order not found in pending_orders: {}", order_id);
        }

        Ok(())
    }

    /// 获取订单簿快照
    pub async fn get_orderbook_snapshot(
        &self,
    ) -> (
        std::collections::BTreeMap<Price, f64>,
        std::collections::BTreeMap<Price, f64>,
    ) {
        let (bids, asks, _, _) = self.orderbook.lock().await.snapshot();
        (bids, asks)
    }

    /// 更新账户管理器的价格信息
    async fn update_account_prices(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::Bbo(bbo) => {
                // 使用中间价更新价格
                let mid_price = (bbo.bid_price.value() + bbo.ask_price.value()) / 2.0;
                // 假设交易对为BTCUSDT，实际应该从市场数据中获取
                self.update_current_price("BTCUSDT".to_string(), mid_price);
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price("BTCUSDT".to_string(), Price::new(mid_price));
            }
            MarketData::BookTicker(bookticker) => {
                let mid_price = bookticker.mid_price().value();
                // 假设交易对为BTCUSDT，实际应该从市场数据中获取
                self.update_current_price("BTCUSDT".to_string(), mid_price);
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price("BTCUSDT".to_string(), Price::new(mid_price));
            }
            MarketData::Trade(trade) => {
                self.update_current_price(trade.symbol.clone(), trade.price.value());
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price(trade.symbol.clone(), trade.price);
            }
            MarketData::TradeData(trade_data) => {
                self.update_current_price(trade_data.symbol.clone(), trade_data.price.value());
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price(trade_data.symbol.clone(), trade_data.price);
            }
            MarketData::OrderBook(_) => {
                // 订单簿快照不包含单一价格，跳过
            }
        }
    }

    /// 获取账户摘要
    pub async fn get_account_summary(&self) -> crate::account::account::AccountSummary {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_account_summary()
    }

    /// 获取账户余额
    pub async fn get_account_balance(&self, asset: &str) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_balance(asset)
    }

    /// 获取账户净值
    pub async fn get_account_net_value(&self) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_net_value()
    }

    /// 获取仓位信息
    pub async fn get_position(&self, symbol: &str) -> Option<crate::account::position::Position> {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_position(symbol).cloned()
    }

    /// 获取当前市场价格
    fn get_current_market_price(&self, symbol: &str) -> Option<f64> {
        self.current_prices.get(symbol).copied()
    }

    /// 更新当前市场价格
    fn update_current_price(&mut self, symbol: String, price: f64) {
        self.current_prices.insert(symbol, price);
    }

    /// 记录订单到回测记录器
    async fn record_order(&self, order: &Order) {
        use crate::state::get_backtest_recorder;

        debug!(
            "🔍 Recording order: {} {} {} @ {:?} (status: {:?}, filled_qty: {:.4}, exec_info: {:?})",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price,
            order.status,
            order.execution_info.as_ref().map(|ei| ei.filled_quantity).unwrap_or(0.0),
            order.execution_info.as_ref().map(|ei| format!("avg_price: {:?}, commission: {:.4}", ei.average_price, ei.commission))
        );

        if let Some(recorder) = get_backtest_recorder().await {
            let mut recorder = recorder.lock().await;

            // 确保使用市场数据时间戳记录订单
            let mut order_to_record = order.clone();
            if self.current_market_timestamp > 0 {
                order_to_record.timestamp =
                    DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                        .unwrap_or_else(|| order.timestamp);
            }

            recorder.record_order(&order_to_record);
            debug!("✅ Order {} recorded to backtest recorder", order.id);
        } else {
            warn!("❌ Backtest recorder not available for order {}", order.id);
        }
    }

    /// 记录市场数据到回测记录器（在流控之后）
    async fn record_market_data_after_flow_control(&self, market_data: &MarketData) {
        use crate::state::get_backtest_recorder;

        // 只记录BBO数据
        if let MarketData::Bbo(bbo) = market_data {
            if let Some(recorder) = get_backtest_recorder().await {
                let mut recorder = recorder.lock().await;
                recorder.record_bbo(bbo);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::account::types::AccountConfig;
    use crate::types::{OrderStatus, OrderType};
    use chrono::Utc;
    use tokio::sync::{broadcast, mpsc};
    use tokio::time::Instant;

    fn create_test_account_manager() -> Arc<Mutex<AccountManager>> {
        let config = AccountConfig::default();
        Arc::new(Mutex::new(AccountManager::new(
            "test_account".to_string(),
            config,
        )))
    }

    fn create_test_order(id: &str, side: OrderSide, price: Option<Price>, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            client_order_id: format!("client_{}", id),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side,
            price,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        }
    }

    fn create_market_order(id: &str, side: OrderSide, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            client_order_id: format!("client_{}", id),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Market,
            side,
            price: None,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        }
    }

    #[tokio::test]
    async fn test_matching_engine_limit_order_matching() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建买单进行撮合
        let mut buy_order = create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 5.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态
        assert_eq!(buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 5.0);
        assert_eq!(trade.price, Price::new(100.0));

        // 验证订单更新
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
    }

    #[tokio::test]
    async fn test_matching_engine_market_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 设置BBO数据，这样市价单就能以正确的价格成交
        let bbo = Bbo {
            update_id: 0,
            bid_price: Price::new(99.0),
            bid_quantity: 5.0,
            ask_price: Price::new(100.0),
            ask_quantity: 10.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };
        engine.current_bbo.insert("BTCUSDT".to_string(), bbo);

        // 创建市价买单
        let mut market_buy_order = create_market_order("buy1", OrderSide::Buy, 3.0);

        // 执行撮合
        let result = engine.match_market_order(&mut market_buy_order).await;
        assert!(result.is_ok());

        // 验证市价单状态
        assert_eq!(market_buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0);
        assert_eq!(trade.price, Price::new(100.0));
    }

    #[tokio::test]
    async fn test_matching_engine_gtx_order_cancelled() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 设置BBO数据，使GTX买单会立即成交（成为taker）
        let bbo = Bbo {
            update_id: 0,
            bid_price: Price::new(99.0),
            bid_quantity: 5.0,
            ask_price: Price::new(100.0),
            ask_quantity: 10.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };
        engine.current_bbo.insert("BTCUSDT".to_string(), bbo);

        // 创建GTX买单，价格等于ask价格，会立即成交（成为taker）
        let mut gtx_buy_order = Order {
            id: "gtx_buy1".to_string(),
            client_order_id: "client_gtx_buy1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::LimitGTX,
            side: OrderSide::Buy,
            price: Some(Price::new(100.0)), // 等于ask价格，会立即成交
            quantity: 3.0,
            status: OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 执行GTX撮合
        let result = engine.match_gtx_order(&mut gtx_buy_order).await;
        assert!(result.is_ok());

        // 验证GTX订单被取消（因为会成为taker）
        assert_eq!(gtx_buy_order.status, OrderStatus::Cancelled);

        // 验证没有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_err()); // 应该没有交易
    }

    #[tokio::test]
    async fn test_matching_engine_gtx_order_as_maker() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 设置BBO数据
        let bbo = Bbo {
            update_id: 0,
            bid_price: Price::new(99.0),
            bid_quantity: 5.0,
            ask_price: Price::new(101.0), // ask价格为101
            ask_quantity: 10.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };
        engine.current_bbo.insert("BTCUSDT".to_string(), bbo);

        // 创建GTX买单，价格低于ask价格，不会立即成交（作为maker）
        let mut gtx_buy_order = Order {
            id: "gtx_buy1".to_string(),
            client_order_id: "client_gtx_buy1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::LimitGTX,
            side: OrderSide::Buy,
            price: Some(Price::new(100.0)), // 低于ask价格，不会立即成交
            quantity: 3.0,
            status: OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 执行GTX撮合
        let result = engine.match_gtx_order(&mut gtx_buy_order).await;
        assert!(result.is_ok());

        // 验证GTX订单挂在订单簿中（作为maker）
        assert_eq!(gtx_buy_order.status, OrderStatus::Pending);

        // 验证订单被添加到订单簿和待处理订单中
        assert!(engine.pending_orders.contains_key("gtx_buy1"));
    }

    #[tokio::test]
    async fn test_matching_engine_partial_fill() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加小的卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 3.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建大的买单进行撮合
        let mut buy_order =
            create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 10.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态（应该是部分成交）
        assert_eq!(buy_order.status, OrderStatus::PartiallyFilled);
        assert_eq!(buy_order.quantity, 7.0); // 剩余7.0未成交

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0); // 成交3.0

        // 验证买单被加入到待处理订单
        assert!(engine.pending_orders.contains_key("buy1"));
    }

    #[tokio::test]
    async fn test_matching_engine_cancel_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加订单到待处理列表
        let order = create_test_order("test1", OrderSide::Buy, Some(Price::new(99.0)), 10.0);
        engine
            .pending_orders
            .insert("test1".to_string(), order.clone());
        engine.orderbook.add_order(order);

        // 取消订单
        let result = engine.cancel_order("test1").await;
        assert!(result.is_ok());

        // 验证订单被移除
        assert!(!engine.pending_orders.contains_key("test1"));

        // 验证订单更新通知
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
        let updated_order = order_update.unwrap();
        assert_eq!(updated_order.status, OrderStatus::Cancelled);
    }

    #[tokio::test]
    async fn test_time_sync_buffer_overflow() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        let base_timestamp = 1640995200000000u64;

        // 添加多条数据
        for i in 0..10 {
            let bbo = Bbo {
                update_id: i,
                bid_price: Price::new(99.5),
                bid_quantity: 10.0,
                ask_price: Price::new(100.5),
                ask_quantity: 15.0,
                timestamp: Some(base_timestamp + i * 1000),
                data_source_type: crate::config::DataSourceType::BinanceOfficial,
            };

            let result = engine.process_market_data(MarketData::Bbo(bbo)).await;
            assert!(result.is_ok());
        }

        // 验证时间对齐器中有数据
        assert!(engine.get_time_aligner_size() > 0);
    }

    #[tokio::test]
    async fn test_playback_rate_control() {
        use crate::config::PlaybackConfig;

        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));

        // 创建回放配置：每秒5条，批处理2条
        let playback_config = PlaybackConfig {
            rate_per_second: 5,
            enabled: true,
            batch_size: 2,
            time_alignment: crate::config::TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        // 创建虚拟的取消订单通道
        let (_cancel_order_tx, cancel_order_rx) = mpsc::channel::<CancelOrderRequest>(1);
        let (_order_tx, order_rx) = mpsc::channel::<Order>(1);

        let mut engine = MatchingEngine::new_with_playback_config(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            cancel_order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            playback_config,
        );

        let base_timestamp = 1640995200000000u64;

        // 添加多条数据到缓冲区
        for i in 0..10 {
            let bbo = Bbo {
                update_id: i,
                bid_price: Price::new(99.5),
                bid_quantity: 10.0,
                ask_price: Price::new(100.5),
                ask_quantity: 15.0,
                timestamp: Some(base_timestamp + i * 1000),
                data_source_type: crate::config::DataSourceType::BinanceOfficial,
            };

            let result = engine.add_to_buffer_only(MarketData::Bbo(bbo)).await;
            assert!(result.is_ok());
        }

        // 验证时间对齐器中有数据
        assert_eq!(engine.get_time_aligner_size(), 10);

        // 记录开始时间
        let start_time = Instant::now();

        // 强制处理缓冲区数据（应该受到速率控制）
        let result = engine.process_buffered_data().await;
        assert!(result.is_ok());

        // 记录结束时间
        let elapsed = start_time.elapsed();

        // 验证时间对齐器已清空
        assert_eq!(engine.get_time_aligner_size(), 0);

        // 验证处理时间：10条数据，每秒5条，应该至少需要1秒多一点
        // 但由于批处理和测试环境的不确定性，我们只检查是否有明显的延迟
        println!("Processing 10 items took: {:?}", elapsed);

        // 验证回放配置
        assert_eq!(engine.get_playback_config().rate_per_second, 5);
        assert_eq!(engine.get_playback_config().batch_size, 2);
        assert!(engine.get_playback_config().enabled);
    }

    #[tokio::test]
    async fn test_playback_config_update() {
        use crate::config::PlaybackConfig;

        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 验证默认配置
        let default_config = engine.get_playback_config();
        assert_eq!(default_config.rate_per_second, 1000);
        assert!(default_config.enabled);
        assert_eq!(default_config.batch_size, 10);

        // 更新配置
        let new_config = PlaybackConfig {
            rate_per_second: 500,
            enabled: false,
            batch_size: 5,
            time_alignment: crate::config::TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        engine.update_playback_config(new_config.clone());

        // 验证配置已更新
        let updated_config = engine.get_playback_config();
        assert_eq!(updated_config.rate_per_second, 500);
        assert!(!updated_config.enabled);
        assert_eq!(updated_config.batch_size, 5);
    }

    #[tokio::test]
    async fn test_delayed_orders_use_latest_market_data() {
        use crate::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig};
        use crate::types::{Bbo, OrderSide, OrderType};

        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 创建启用订单延迟的配置
        let mut playback_config = PlaybackConfig::default();
        playback_config.order_latency = OrderLatencyConfig {
            enabled: true,
            latency_micros: 1000, // 1毫秒延迟
            max_queue_size: 100,
            random_latency: false,
        };

        // 创建虚拟的取消订单通道
        let (_cancel_order_tx, cancel_order_rx) = mpsc::channel::<CancelOrderRequest>(1);
        let (_order_tx, order_rx) = mpsc::channel::<Order>(1);

        let mut engine = MatchingEngine::new_with_playback_config(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            cancel_order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            playback_config,
        );

        let base_timestamp = chrono::Utc::now().timestamp_micros() as u64;

        // 1. 处理第一个BBO数据（较高的ask价格）
        let bbo1 = Bbo {
            update_id: 1,
            bid_price: Price::new(99.0),
            bid_quantity: 10.0,
            ask_price: Price::new(101.0), // 高ask价格
            ask_quantity: 15.0,
            timestamp: Some(base_timestamp),
            data_source_type: DataSourceType::BinanceTardis,
        };

        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo1))
            .await
            .unwrap();

        // 2. 添加一个市价买单（会被延迟）
        let market_order = Order {
            id: "delayed_buy".to_string(),
            client_order_id: "client_delayed_buy".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Market,
            side: OrderSide::Buy,
            price: None,
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 添加订单到延迟队列
        engine
            .order_latency_simulator
            .add_order(market_order.clone(), base_timestamp)
            .unwrap();

        // 3. 处理第二个BBO数据（较低的ask价格）- 延迟订单应该使用这个价格
        let bbo2 = Bbo {
            update_id: 2,
            bid_price: Price::new(99.5),
            bid_quantity: 10.0,
            ask_price: Price::new(100.0), // 更低的ask价格
            ask_quantity: 15.0,
            timestamp: Some(base_timestamp + 1500), // 超过延迟时间
            data_source_type: DataSourceType::BinanceTardis,
        };

        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo2))
            .await
            .unwrap();

        // 4. 验证交易是否以最新的BBO价格执行
        let trade = trade_rx.try_recv().unwrap();
        assert_eq!(trade.price, Price::new(100.0)); // 应该使用最新的ask价格
        assert_eq!(trade.quantity, 1.0);
        assert_eq!(trade.side, OrderSide::Buy);
    }

    #[tokio::test]
    async fn test_margin_freeze_and_release() {
        use crate::config::DataSourceType;
        use crate::types::{Bbo, OrderSide, OrderType};

        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 记录初始余额
        let initial_balance = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        // 设置当前市场价格
        let bbo = Bbo {
            update_id: 1,
            bid_price: Price::new(99.0),
            bid_quantity: 10.0,
            ask_price: Price::new(101.0),
            ask_quantity: 15.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: DataSourceType::BinanceTardis,
        };
        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo))
            .await
            .unwrap();

        // 创建一个限价买单
        let limit_order = Order {
            id: "test_limit_buy".to_string(),
            client_order_id: "client_test_limit_buy".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(98.0)), // 低于当前bid价格，不会立即成交
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 直接处理订单
        engine.process_order(limit_order.clone()).await.unwrap();

        // 验证保证金被冻结
        let balance_after_order = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        assert!(
            balance_after_order < initial_balance,
            "Balance should decrease after placing order (margin frozen)"
        );

        // 验证订单在冻结保证金记录中
        assert!(
            engine.frozen_margins.contains_key(&limit_order.id),
            "Order should have frozen margin record"
        );

        // 处理订单创建的更新消息
        let order_create_update = order_update_rx.try_recv().unwrap();
        assert_eq!(
            order_create_update.status,
            crate::types::OrderStatus::Pending
        );
        assert_eq!(order_create_update.id, limit_order.id);

        // 取消订单
        engine.cancel_order(&limit_order.id).await.unwrap();

        // 验证保证金被释放
        let balance_after_cancel = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        assert_eq!(
            balance_after_cancel, initial_balance,
            "Balance should return to initial value after order cancellation"
        );

        // 验证冻结保证金记录被清除
        assert!(
            !engine.frozen_margins.contains_key(&limit_order.id),
            "Frozen margin record should be removed after cancellation"
        );

        // 验证订单取消的更新消息
        let order_cancel_update = order_update_rx.try_recv().unwrap();
        assert_eq!(
            order_cancel_update.status,
            crate::types::OrderStatus::Cancelled
        );
        assert_eq!(order_cancel_update.id, limit_order.id);
    }

    #[tokio::test]
    async fn test_ioc_order_margin_handling() {
        use crate::config::DataSourceType;
        use crate::types::{Bbo, OrderSide, OrderType};

        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 记录初始余额
        let initial_balance = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        // 设置当前市场价格
        let bbo = Bbo {
            update_id: 1,
            bid_price: Price::new(99.0),
            bid_quantity: 10.0,
            ask_price: Price::new(101.0),
            ask_quantity: 15.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: DataSourceType::BinanceTardis,
        };
        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo))
            .await
            .unwrap();

        // 测试1: IOC订单成交的情况
        let ioc_order_filled = Order {
            id: "test_ioc_filled".to_string(),
            client_order_id: "client_test_ioc_filled".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::LimitIOC,
            side: OrderSide::Buy,
            price: Some(Price::new(102.0)), // 高于ask价格，会立即成交
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 处理IOC订单（会成交）
        engine
            .process_order(ioc_order_filled.clone())
            .await
            .unwrap();

        // 验证交易发生
        let trade = trade_rx.try_recv().unwrap();
        assert_eq!(trade.side, OrderSide::Buy);
        assert_eq!(trade.quantity, 1.0);

        // 验证订单状态更新
        let order_update = order_update_rx.try_recv().unwrap();
        assert_eq!(order_update.status, crate::types::OrderStatus::Filled);

        // 验证保证金被释放（余额应该接近初始值，只扣除了手续费）
        let balance_after_filled = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };
        assert!(
            balance_after_filled < initial_balance,
            "Balance should decrease due to fees"
        );
        assert!(
            balance_after_filled > initial_balance - 1000.0,
            "Balance should not decrease too much"
        );

        // 测试2: IOC订单过期的情况
        let ioc_order_expired = Order {
            id: "test_ioc_expired".to_string(),
            client_order_id: "client_test_ioc_expired".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::LimitIOC,
            side: OrderSide::Buy,
            price: Some(Price::new(95.0)), // 低于bid价格，不会成交
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 记录处理IOC过期订单前的余额
        let balance_before_expired = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        // 处理IOC订单（会过期）
        engine
            .process_order(ioc_order_expired.clone())
            .await
            .unwrap();

        // 验证没有交易发生
        assert!(
            trade_rx.try_recv().is_err(),
            "No trade should occur for expired IOC order"
        );

        // 验证订单状态更新
        let order_update = order_update_rx.try_recv().unwrap();
        assert_eq!(order_update.status, crate::types::OrderStatus::Expired);

        // 验证保证金被释放（余额应该回到处理前的水平）
        let balance_after_expired = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };
        assert_eq!(
            balance_after_expired, balance_before_expired,
            "Balance should return to previous level after IOC order expiration"
        );

        // 验证没有冻结保证金记录
        assert!(
            !engine.frozen_margins.contains_key(&ioc_order_filled.id),
            "No frozen margin should remain for filled IOC order"
        );
        assert!(
            !engine.frozen_margins.contains_key(&ioc_order_expired.id),
            "No frozen margin should remain for expired IOC order"
        );
    }

    #[tokio::test]
    async fn test_limit_order_margin_handling() {
        use crate::config::DataSourceType;
        use crate::types::{Bbo, OrderSide, OrderType};

        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 记录初始余额
        let initial_balance = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        // 设置当前市场价格
        let bbo = Bbo {
            update_id: 1,
            bid_price: Price::new(99.0),
            bid_quantity: 10.0,
            ask_price: Price::new(101.0),
            ask_quantity: 15.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: DataSourceType::BinanceTardis,
        };
        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo))
            .await
            .unwrap();

        // 创建一个限价买单（不会立即成交，先挂在订单簿中）
        let limit_order = Order {
            id: "test_limit_pending".to_string(),
            client_order_id: "client_test_limit_pending".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(98.0)), // 低于ask价格，不会立即成交
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 处理限价订单（会挂在订单簿中）
        engine.process_order(limit_order.clone()).await.unwrap();

        // 验证订单状态更新（应该是Pending）
        let order_update = order_update_rx.try_recv().unwrap();
        assert_eq!(order_update.status, crate::types::OrderStatus::Pending);

        // 验证订单在pending_orders中
        assert!(engine.pending_orders.contains_key(&limit_order.id));

        // 现在发送一个新的BBO，使订单能够成交
        let new_bbo = Bbo {
            update_id: 2,
            bid_price: Price::new(98.5),
            bid_quantity: 10.0,
            ask_price: Price::new(98.0), // ask价格降到订单价格，会触发成交
            ask_quantity: 15.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: DataSourceType::BinanceTardis,
        };
        engine
            .process_single_market_data_internal(MarketData::Bbo(new_bbo))
            .await
            .unwrap();

        // 验证交易发生
        let trade = trade_rx.try_recv().unwrap();
        assert_eq!(trade.side, OrderSide::Buy);
        assert_eq!(trade.quantity, 1.0);

        // 验证订单状态更新（应该是Filled）
        let order_update = order_update_rx.try_recv().unwrap();
        assert_eq!(order_update.status, crate::types::OrderStatus::Filled);

        // 验证保证金被释放
        let balance_after_filled = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };
        assert!(
            balance_after_filled < initial_balance,
            "Balance should decrease due to fees"
        );
        assert!(
            balance_after_filled > initial_balance - 1000.0,
            "Balance should not decrease too much"
        );

        // 验证没有冻结保证金记录
        assert!(
            !engine.frozen_margins.contains_key(&limit_order.id),
            "No frozen margin should remain for filled limit order"
        );
    }

    #[tokio::test]
    async fn test_account_balance_reflects_frozen_margin() {
        use crate::config::DataSourceType;
        use crate::types::{Bbo, OrderSide, OrderType};

        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let orderbook = Arc::new(Mutex::new(OrderBook::new()));
        let mut engine = MatchingEngine::new(
            orderbook,
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 记录初始余额
        let initial_balance = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        // 设置当前市场价格
        let bbo = Bbo {
            update_id: 1,
            bid_price: Price::new(99.0),
            bid_quantity: 10.0,
            ask_price: Price::new(101.0),
            ask_quantity: 15.0,
            timestamp: Some(chrono::Utc::now().timestamp_micros() as u64),
            data_source_type: DataSourceType::BinanceTardis,
        };
        engine
            .process_single_market_data_internal(MarketData::Bbo(bbo))
            .await
            .unwrap();

        // 创建一个限价买单（不会立即成交）
        let limit_order = Order {
            id: "test_balance_check".to_string(),
            client_order_id: "client_test_balance_check".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(98.0)), // 低于ask价格，不会立即成交
            quantity: 1.0,
            status: crate::types::OrderStatus::Pending,
            timestamp: chrono::Utc::now(),
            execution_info: None,
        };

        // 处理限价订单（会挂在订单簿中并冻结保证金）
        engine.process_order(limit_order.clone()).await.unwrap();

        // 验证订单状态更新
        let order_update = order_update_rx.try_recv().unwrap();
        assert_eq!(order_update.status, crate::types::OrderStatus::Pending);

        // 验证保证金被冻结 - 通过账户管理器直接检查
        let balance_after_order = {
            let account = account_manager.lock().await;
            account.get_balance("USDT")
        };

        assert!(balance_after_order < initial_balance,
            "Available balance should decrease after placing order (margin frozen). Initial: {}, After: {}",
            initial_balance, balance_after_order);

        // 验证账户摘要也反映了保证金冻结
        let account_summary = {
            let account = account_manager.lock().await;
            account.get_account_summary()
        };

        assert!(account_summary.stats.available_balance < initial_balance,
            "Account summary available_balance should also reflect frozen margin. Initial: {}, Summary: {}",
            initial_balance, account_summary.stats.available_balance);

        // 验证冻结保证金记录存在
        assert!(
            engine.frozen_margins.contains_key(&limit_order.id),
            "Frozen margin record should exist for pending order"
        );

        println!("✅ Test passed: Account balance correctly reflects frozen margin");
        println!("   Initial balance: {}", initial_balance);
        println!("   Balance after order: {}", balance_after_order);
        println!(
            "   Summary available balance: {}",
            account_summary.stats.available_balance
        );
        println!(
            "   Frozen margin amount: {}",
            engine.frozen_margins.get(&limit_order.id).unwrap_or(&0.0)
        );
    }
}
