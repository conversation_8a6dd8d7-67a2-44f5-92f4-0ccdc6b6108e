use backtest::types::{BacktestRecorder, Bbo, Price};
use backtest::config::DataSourceType;
use chrono::{DateTime, Utc, Duration};
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 BBO聚合功能测试");
    println!("==================");

    // 1. 创建回测记录器
    println!("1. 创建回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::with_config(
        1000, // 最多保留1000个OHLC数据点
        100,  // 最多保留100个原始BBO记录
    )));
    println!("   ✓ 回测记录器创建成功");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.start_recording(Utc::now());
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟大量BBO数据（跨越多秒）
    println!("3. 模拟大量BBO数据...");
    let base_time = Utc::now();
    let mut total_bbo_count = 0;

    // 模拟10秒的数据，每秒100个BBO数据点
    for second in 0..10 {
        for tick in 0..100 {
            let timestamp = base_time + Duration::seconds(second) + Duration::milliseconds(tick * 10);
            
            // 模拟价格波动
            let base_price = 50000.0 + (second as f64 * 10.0);
            let price_variation = (tick as f64 / 100.0) * 20.0; // 每秒内价格变化20美元
            let bid_price = base_price + price_variation;
            let ask_price = bid_price + 1.0;

            let bbo = Bbo {
                update_id: total_bbo_count,
                bid_price: Price::new(bid_price),
                bid_quantity: 1.0 + (tick as f64 / 100.0),
                ask_price: Price::new(ask_price),
                ask_quantity: 1.0 + (tick as f64 / 100.0),
                timestamp: Some(timestamp.timestamp_micros() as u64),
                data_source_type: DataSourceType::BinanceOfficial,
            };

            {
                let mut recorder_guard = recorder.lock().await;
                recorder_guard.record_bbo(&bbo);
            }
            
            total_bbo_count += 1;
        }
        
        // 每秒打印一次进度
        let recorder_guard = recorder.lock().await;
        let memory_stats = recorder_guard.get_memory_stats();
        println!("   第{}秒: 原始BBO={}, OHLC={}, 总BBO数={}", 
            second + 1, memory_stats.raw_bbo_count, memory_stats.ohlc_count, total_bbo_count);
        drop(recorder_guard);
    }

    // 4. 检查内存使用情况
    println!("4. 检查内存使用情况...");
    {
        let recorder_guard = recorder.lock().await;
        let memory_stats = recorder_guard.get_memory_stats();
        
        println!("   📊 内存统计:");
        println!("      - 总BBO输入数量: {}", total_bbo_count);
        println!("      - 保留的原始BBO: {}", memory_stats.raw_bbo_count);
        println!("      - 聚合的OHLC数据: {}", memory_stats.ohlc_count);
        println!("      - 订单数量: {}", memory_stats.orders_count);
        println!("      - 交易数量: {}", memory_stats.trades_count);
        
        // 计算内存节省比例
        let compression_ratio = if memory_stats.ohlc_count > 0 {
            total_bbo_count as f64 / memory_stats.ohlc_count as f64
        } else {
            0.0
        };
        
        println!("      - 压缩比例: {:.1}:1 (每个OHLC代表{:.1}个BBO数据点)", 
            compression_ratio, compression_ratio);
    }

    // 5. 查看OHLC数据详情
    println!("5. 查看OHLC数据详情...");
    {
        let recorder_guard = recorder.lock().await;
        let ohlc_data = recorder_guard.get_ohlc_data();
        
        println!("   📈 前5个OHLC数据:");
        for (i, ohlc) in ohlc_data.iter().take(5).enumerate() {
            println!("      {}. 时间: {}, O: {:.2}, H: {:.2}, L: {:.2}, C: {:.2}, V: {}",
                i + 1,
                ohlc.timestamp.format("%H:%M:%S"),
                ohlc.open,
                ohlc.high,
                ohlc.low,
                ohlc.close,
                ohlc.volume
            );
        }
        
        if let Some(current_ohlc) = recorder_guard.get_current_ohlc() {
            println!("   📊 当前聚合中的OHLC:");
            println!("      时间: {}, O: {:.2}, H: {:.2}, L: {:.2}, C: {:.2}, V: {}",
                current_ohlc.timestamp.format("%H:%M:%S"),
                current_ohlc.open,
                current_ohlc.high,
                current_ohlc.low,
                current_ohlc.close,
                current_ohlc.volume
            );
        }
    }

    // 6. 停止记录并完成聚合
    println!("6. 停止记录...");
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.stop_recording(Utc::now());
    }
    println!("   ✓ 回测记录已停止，最后的OHLC已完成聚合");

    // 7. 生成回测总结
    println!("7. 生成回测总结...");
    let summary = {
        let recorder_guard = recorder.lock().await;
        recorder_guard.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");
            println!("   📊 总结统计:");
            println!("      - 回测期间: {} - {}",
                summary.start_time.format("%H:%M:%S"),
                summary.end_time.format("%H:%M:%S")
            );
            println!("      - 原始BBO记录: {}", summary.bbo_records.len());
            println!("      - OHLC聚合数据: {}", summary.ohlc_data.len());
            println!("      - 内存节省: {:.1}%", 
                (1.0 - (summary.ohlc_data.len() as f64 / total_bbo_count as f64)) * 100.0);
        }
        None => {
            println!("   ❌ 回测总结生成失败");
        }
    }

    println!("✅ BBO聚合功能测试完成");
    println!("💡 通过实时聚合，大大减少了内存使用，同时保持了K线图的完整性");

    Ok(())
}
