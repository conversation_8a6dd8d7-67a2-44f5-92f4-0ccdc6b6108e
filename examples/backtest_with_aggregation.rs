use backtest::types::{BacktestRecorder, Bbo, Price};
use backtest::config::DataSourceType;
use backtest::backtest_summary::HtmlGenerator;
use chrono::{Utc, Duration};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 带聚合功能的回测示例");
    println!("========================");

    // 1. 创建回测记录器（配置更大的数据量）
    println!("1. 创建回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::with_config(
        5000, // 最多保留5000个OHLC数据点（约1.4小时的秒级数据）
        500,  // 最多保留500个原始BBO记录用于调试
    )));
    println!("   ✓ 回测记录器创建成功");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    let start_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.start_recording(start_time);
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟长时间的市场数据（模拟1小时的数据）
    println!("3. 模拟市场数据（1小时，每秒50个BBO数据点）...");
    let base_time = start_time;
    let mut total_bbo_count = 0;
    let total_seconds = 60; // 模拟1分钟（实际应用中可以是几小时）
    let bbo_per_second = 50;

    for second in 0..total_seconds {
        for tick in 0..bbo_per_second {
            let timestamp = base_time + Duration::seconds(second) + Duration::milliseconds(tick * 20);
            
            // 模拟更真实的价格波动（包括趋势和随机波动）
            let trend = second as f64 * 0.5; // 缓慢上升趋势
            let cycle = ((second as f64 * 0.1).sin() * 10.0); // 周期性波动
            let random = ((tick as f64 * 0.3).sin() * 5.0); // 随机波动
            
            let base_price = 50000.0 + trend + cycle + random;
            let bid_price = base_price;
            let ask_price = bid_price + 0.5 + (tick as f64 / 100.0); // 动态点差

            let bbo = Bbo {
                update_id: total_bbo_count,
                bid_price: Price::new(bid_price),
                bid_quantity: 1.0 + (tick as f64 / 50.0),
                ask_price: Price::new(ask_price),
                ask_quantity: 1.0 + (tick as f64 / 50.0),
                timestamp: Some(timestamp.timestamp_micros() as u64),
                data_source_type: DataSourceType::BinanceOfficial,
            };

            {
                let mut recorder_guard = recorder.lock().await;
                recorder_guard.record_bbo(&bbo);
            }
            
            total_bbo_count += 1;
        }
        
        // 每10秒打印一次进度
        if (second + 1) % 10 == 0 {
            let recorder_guard = recorder.lock().await;
            let memory_stats = recorder_guard.get_memory_stats();
            println!("   进度: {}/{}秒, 原始BBO={}, OHLC={}, 压缩比={:.1}:1", 
                second + 1, total_seconds, 
                memory_stats.raw_bbo_count, 
                memory_stats.ohlc_count,
                total_bbo_count as f64 / memory_stats.ohlc_count.max(1) as f64
            );
            drop(recorder_guard);
        }
    }

    // 4. 停止记录
    println!("4. 停止记录...");
    let end_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.stop_recording(end_time);
    }
    println!("   ✓ 回测记录已停止");

    // 5. 分析内存使用效果
    println!("5. 分析内存使用效果...");
    {
        let recorder_guard = recorder.lock().await;
        let memory_stats = recorder_guard.get_memory_stats();
        
        println!("   📊 最终内存统计:");
        println!("      - 总BBO输入数量: {}", total_bbo_count);
        println!("      - 保留的原始BBO: {}", memory_stats.raw_bbo_count);
        println!("      - 聚合的OHLC数据: {}", memory_stats.ohlc_count);
        
        let compression_ratio = total_bbo_count as f64 / memory_stats.ohlc_count.max(1) as f64;
        let memory_saved = (1.0 - (memory_stats.ohlc_count as f64 / total_bbo_count as f64)) * 100.0;
        
        println!("      - 压缩比例: {:.1}:1", compression_ratio);
        println!("      - 内存节省: {:.1}%", memory_saved);
        
        // 估算内存使用量（假设每个BBO记录约100字节，每个OHLC记录约80字节）
        let original_memory_mb = (total_bbo_count * 100) as f64 / 1024.0 / 1024.0;
        let current_memory_mb = (memory_stats.raw_bbo_count * 100 + memory_stats.ohlc_count * 80) as f64 / 1024.0 / 1024.0;
        
        println!("      - 原始内存需求: {:.2} MB", original_memory_mb);
        println!("      - 实际内存使用: {:.2} MB", current_memory_mb);
        println!("      - 内存节省量: {:.2} MB", original_memory_mb - current_memory_mb);
    }

    // 6. 生成回测总结和HTML报告
    println!("6. 生成回测总结和HTML报告...");
    let summary = {
        let recorder_guard = recorder.lock().await;
        recorder_guard.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");
            
            // 生成HTML报告
            let html = HtmlGenerator::generate_summary_html(&summary);
            
            // 保存HTML文件
            let filename = format!(
                "backtest_aggregation_demo_{}.html",
                Utc::now().format("%Y%m%d_%H%M%S")
            );
            
            fs::write(&filename, html)?;
            
            println!("   📄 HTML报告已保存: {}", filename);
            println!("   📊 报告统计:");
            println!("      - 回测时长: {:.1}分钟", 
                (summary.end_time - summary.start_time).num_seconds() as f64 / 60.0);
            println!("      - 原始BBO记录: {}", summary.bbo_records.len());
            println!("      - OHLC聚合数据: {}", summary.ohlc_data.len());
            println!("      - 内存使用统计: {:?}", summary.memory_stats);
        }
        None => {
            println!("   ❌ 回测总结生成失败");
        }
    }

    // 7. 展示OHLC数据样本
    println!("7. 展示OHLC数据样本...");
    {
        let recorder_guard = recorder.lock().await;
        let ohlc_data = recorder_guard.get_ohlc_data();
        
        println!("   📈 前10个OHLC数据:");
        for (i, ohlc) in ohlc_data.iter().take(10).enumerate() {
            println!("      {}. {} | O:{:.2} H:{:.2} L:{:.2} C:{:.2} V:{}",
                i + 1,
                ohlc.timestamp.format("%H:%M:%S"),
                ohlc.open,
                ohlc.high,
                ohlc.low,
                ohlc.close,
                ohlc.volume
            );
        }
        
        if ohlc_data.len() > 10 {
            println!("      ... 还有 {} 个OHLC数据点", ohlc_data.len() - 10);
        }
    }

    println!("✅ 带聚合功能的回测示例完成");
    println!("💡 关键优势:");
    println!("   - 大幅减少内存使用（90%+节省）");
    println!("   - 保持K线图数据完整性");
    println!("   - 实时聚合，无需后处理");
    println!("   - 支持长时间回测而不会内存溢出");

    Ok(())
}
