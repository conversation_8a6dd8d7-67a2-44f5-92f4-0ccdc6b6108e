use backtest::backtest_summary::HtmlGenerator;
use backtest::config::DataSourceType;
use backtest::types::{
    BacktestRecorder, Bbo, Order, OrderExecutionInfo, OrderSide, OrderStatus, OrderType, Price,
};
use chrono::{Duration, Utc};
use std::fs;
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 测试所有订单类型的显示");
    println!("==========================");

    // 1. 创建回测记录器
    println!("1. 创建回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::with_config(1000, 200)));
    println!("   ✓ 回测记录器创建成功");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    let start_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.start_recording(start_time);
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟市场数据
    println!("3. 模拟市场数据...");
    let base_time = start_time;
    let mut total_bbo_count = 0;
    let total_seconds = 20;
    let bbo_per_second = 10;

    for second in 0..total_seconds {
        for tick in 0..bbo_per_second {
            let timestamp = base_time + Duration::seconds(second) + Duration::milliseconds(tick * 100);
            
            let base_price = 50000.0 + (second as f64 * 1.0);
            let bid_price = base_price;
            let ask_price = bid_price + 1.0;

            let bbo = Bbo {
                update_id: total_bbo_count,
                bid_price: Price::new(bid_price),
                bid_quantity: 1.0,
                ask_price: Price::new(ask_price),
                ask_quantity: 1.0,
                timestamp: Some(timestamp.timestamp_micros() as u64),
                data_source_type: DataSourceType::BinanceOfficial,
            };

            {
                let mut recorder_guard = recorder.lock().await;
                recorder_guard.record_bbo(&bbo);
            }
            
            total_bbo_count += 1;
        }
    }

    // 4. 添加各种类型的订单
    println!("4. 添加各种类型的订单...");
    
    let orders = vec![
        // 成交订单
        Order {
            id: "filled_buy_1".to_string(),
            client_order_id: "client_filled_buy_1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Market,
            side: OrderSide::Buy,
            price: Some(Price::new(50005.0)),
            quantity: 0.1,
            status: OrderStatus::Filled,
            timestamp: base_time + Duration::seconds(5),
            execution_info: Some(OrderExecutionInfo {
                last_filled_price: Some(Price::new(50005.0)),
                last_filled_quantity: 0.1,
                filled_quantity: 0.1,
                average_price: Some(Price::new(50005.0)),
                commission: 0.01,
                commission_asset: "USDT".to_string(),
                trade_id: Some("trade_1".to_string()),
            }),
        },
        
        // 部分成交订单
        Order {
            id: "partial_sell_1".to_string(),
            client_order_id: "client_partial_sell_1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(50010.0)),
            quantity: 0.2,
            status: OrderStatus::PartiallyFilled,
            timestamp: base_time + Duration::seconds(8),
            execution_info: Some(OrderExecutionInfo {
                last_filled_price: Some(Price::new(50010.0)),
                last_filled_quantity: 0.1,
                filled_quantity: 0.1,
                average_price: Some(Price::new(50010.0)),
                commission: 0.005,
                commission_asset: "USDT".to_string(),
                trade_id: Some("trade_2".to_string()),
            }),
        },
        
        // 取消订单
        Order {
            id: "cancelled_buy_1".to_string(),
            client_order_id: "client_cancelled_buy_1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(49995.0)),
            quantity: 0.15,
            status: OrderStatus::Cancelled,
            timestamp: base_time + Duration::seconds(12),
            execution_info: None,
        },
        
        // 拒绝订单
        Order {
            id: "rejected_sell_1".to_string(),
            client_order_id: "client_rejected_sell_1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Market,
            side: OrderSide::Sell,
            price: None,
            quantity: 10.0, // 数量过大，被拒绝
            status: OrderStatus::Rejected,
            timestamp: base_time + Duration::seconds(15),
            execution_info: None,
        },
        
        // Pending订单
        Order {
            id: "pending_buy_1".to_string(),
            client_order_id: "client_pending_buy_1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(49990.0)),
            quantity: 0.05,
            status: OrderStatus::Pending,
            timestamp: base_time + Duration::seconds(18),
            execution_info: None,
        },
    ];

    {
        let mut recorder_guard = recorder.lock().await;
        for order in orders {
            recorder_guard.record_order(&order);
        }
    }

    println!("   ✓ 添加了5种不同状态的订单");

    // 5. 停止记录
    println!("5. 停止记录...");
    let end_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.stop_recording(end_time);
    }
    println!("   ✓ 回测记录已停止");

    // 6. 生成HTML报告
    println!("6. 生成HTML报告...");
    let summary = {
        let recorder_guard = recorder.lock().await;
        recorder_guard.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");
            
            // 生成HTML报告
            let html = HtmlGenerator::generate_summary_html(&summary);
            
            // 保存HTML文件
            let filename = format!(
                "all_order_types_test_{}.html",
                Utc::now().format("%Y%m%d_%H%M%S")
            );
            
            fs::write(&filename, html)?;
            
            println!("   📄 HTML报告已保存: {}", filename);
            println!("   📊 报告内容:");
            println!("      - OHLC数据点: {}", summary.ohlc_data.len());
            println!("      - 订单数量: {}", summary.orders.len());
            
            // 显示订单统计
            let mut status_counts = std::collections::HashMap::new();
            for order in &summary.orders {
                *status_counts.entry(format!("{:?}", order.status)).or_insert(0) += 1;
            }
            
            println!("   📋 订单状态统计:");
            for (status, count) in status_counts {
                println!("      - {}: {} 个", status, count);
            }
            
            println!("   🎨 图表标记说明:");
            println!("      - 蓝色实心箭头 ↑: 成交买单");
            println!("      - 红色实心箭头 ↓: 成交卖单");
            println!("      - 浅蓝色箭头 ↑: 部分成交/Pending买单");
            println!("      - 粉色箭头 ↓: 部分成交/Pending卖单");
            println!("      - 灰色箭头: 取消订单");
            println!("      - 红色圆圈 ●: 拒绝订单");
        }
        None => {
            println!("   ❌ 回测总结生成失败");
        }
    }

    println!("✅ 所有订单类型测试完成");
    println!("💡 现在HTML报告中应该显示所有类型的订单标记");

    Ok(())
}
