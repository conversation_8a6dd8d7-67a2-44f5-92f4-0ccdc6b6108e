use backtest::backtest_summary::HtmlGenerator;
use backtest::config::DataSourceType;
use backtest::types::{
    BacktestRecorder, Bbo, Order, OrderExecutionInfo, OrderSide, OrderStatus, OrderType, Price,
};
use chrono::{Duration, Utc};
use std::fs;
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 带交易标记的聚合功能测试");
    println!("============================");

    // 1. 创建回测记录器
    println!("1. 创建回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::with_config(1000, 200)));
    println!("   ✓ 回测记录器创建成功");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    let start_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.start_recording(start_time);
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟市场数据和交易
    println!("3. 模拟市场数据和交易...");
    let base_time = start_time;
    let mut total_bbo_count = 0;
    let total_seconds = 30;
    let bbo_per_second = 20;

    for second in 0..total_seconds {
        for tick in 0..bbo_per_second {
            let timestamp =
                base_time + Duration::seconds(second) + Duration::milliseconds(tick * 50);

            // 模拟价格波动
            let base_price = 50000.0 + (second as f64 * 2.0) + (tick as f64 * 0.1);
            let bid_price = base_price;
            let ask_price = bid_price + 1.0;

            let bbo = Bbo {
                update_id: total_bbo_count,
                bid_price: Price::new(bid_price),
                bid_quantity: 1.0,
                ask_price: Price::new(ask_price),
                ask_quantity: 1.0,
                timestamp: Some(timestamp.timestamp_micros() as u64),
                data_source_type: DataSourceType::BinanceOfficial,
            };

            {
                let mut recorder_guard = recorder.lock().await;
                recorder_guard.record_bbo(&bbo);
            }

            total_bbo_count += 1;
        }

        // 每5秒模拟一些交易
        if second % 5 == 0 && second > 0 {
            let trade_time = base_time + Duration::seconds(second);
            let trade_price = 50000.0 + (second as f64 * 2.0);

            // 模拟买单
            let buy_order = Order {
                id: format!("buy_order_{}", second),
                client_order_id: format!("client_buy_{}", second),
                symbol: "BTCUSDT".to_string(),
                order_type: OrderType::Market,
                side: OrderSide::Buy,
                price: Some(Price::new(trade_price)),
                quantity: 0.1,
                status: OrderStatus::Filled,
                timestamp: trade_time,
                execution_info: Some(OrderExecutionInfo {
                    last_filled_price: Some(Price::new(trade_price)),
                    last_filled_quantity: 0.1,
                    filled_quantity: 0.1,
                    average_price: Some(Price::new(trade_price)),
                    commission: 0.01,
                    commission_asset: "USDT".to_string(),
                    trade_id: Some(format!("trade_buy_{}", second)),
                }),
            };

            // 模拟卖单
            let sell_order = Order {
                id: format!("sell_order_{}", second + 1),
                client_order_id: format!("client_sell_{}", second + 1),
                symbol: "BTCUSDT".to_string(),
                order_type: OrderType::Market,
                side: OrderSide::Sell,
                price: Some(Price::new(trade_price + 5.0)),
                quantity: 0.1,
                status: OrderStatus::Filled,
                timestamp: trade_time + Duration::seconds(1),
                execution_info: Some(OrderExecutionInfo {
                    last_filled_price: Some(Price::new(trade_price + 5.0)),
                    last_filled_quantity: 0.1,
                    filled_quantity: 0.1,
                    average_price: Some(Price::new(trade_price + 5.0)),
                    commission: 0.01,
                    commission_asset: "USDT".to_string(),
                    trade_id: Some(format!("trade_sell_{}", second + 1)),
                }),
            };

            {
                let mut recorder_guard = recorder.lock().await;
                recorder_guard.record_order(&buy_order);
                recorder_guard.record_order(&sell_order);
            }

            println!("   第{}秒: 添加了买卖订单，价格 {:.2}", second, trade_price);
        }
    }

    // 4. 停止记录
    println!("4. 停止记录...");
    let end_time = Utc::now();
    {
        let mut recorder_guard = recorder.lock().await;
        recorder_guard.stop_recording(end_time);
    }
    println!("   ✓ 回测记录已停止");

    // 5. 检查数据统计
    println!("5. 检查数据统计...");
    {
        let recorder_guard = recorder.lock().await;
        let memory_stats = recorder_guard.get_memory_stats();

        println!("   📊 数据统计:");
        println!("      - 总BBO输入: {}", total_bbo_count);
        println!("      - 保留原始BBO: {}", memory_stats.raw_bbo_count);
        println!("      - 聚合OHLC数据: {}", memory_stats.ohlc_count);
        println!("      - 订单数量: {}", memory_stats.orders_count);
        println!(
            "      - 压缩比例: {:.1}:1",
            total_bbo_count as f64 / memory_stats.ohlc_count.max(1) as f64
        );
    }

    // 6. 生成HTML报告
    println!("6. 生成HTML报告...");
    let summary = {
        let recorder_guard = recorder.lock().await;
        recorder_guard.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");

            // 生成HTML报告
            let html = HtmlGenerator::generate_summary_html(&summary);

            // 保存HTML文件
            let filename = format!(
                "aggregation_with_trades_test_{}.html",
                Utc::now().format("%Y%m%d_%H%M%S")
            );

            fs::write(&filename, html)?;

            println!("   📄 HTML报告已保存: {}", filename);
            println!("   📊 报告内容:");
            println!("      - OHLC数据点: {}", summary.ohlc_data.len());
            println!("      - 订单数量: {}", summary.orders.len());
            println!("      - 原始BBO记录: {}", summary.bbo_records.len());

            // 显示一些OHLC数据
            println!("   📈 OHLC数据样本:");
            for (i, ohlc) in summary.ohlc_data.iter().take(5).enumerate() {
                println!(
                    "      {}. {} | O:{:.2} H:{:.2} L:{:.2} C:{:.2} V:{}",
                    i + 1,
                    ohlc.timestamp.format("%H:%M:%S"),
                    ohlc.open,
                    ohlc.high,
                    ohlc.low,
                    ohlc.close,
                    ohlc.volume
                );
            }

            // 显示订单信息
            println!("   📋 订单信息:");
            for order in &summary.orders {
                println!(
                    "      - {} {} {:.2} @ {:.2} ({:?})",
                    order.timestamp.format("%H:%M:%S"),
                    match order.side {
                        backtest::types::OrderSide::Buy => "买入",
                        backtest::types::OrderSide::Sell => "卖出",
                    },
                    order.quantity,
                    order.filled_price.map(|p| p.value()).unwrap_or(0.0),
                    order.status
                );
            }
        }
        None => {
            println!("   ❌ 回测总结生成失败");
        }
    }

    println!("✅ 带交易标记的聚合功能测试完成");
    println!("💡 现在HTML报告中应该包含:");
    println!("   - 基于OHLC聚合数据的K线图");
    println!("   - 买卖交易标记点位");
    println!("   - 大幅减少的内存使用");

    Ok(())
}
